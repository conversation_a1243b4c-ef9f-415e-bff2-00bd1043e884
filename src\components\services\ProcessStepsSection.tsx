import { useTranslation } from "react-i18next";
import { ProcessStep } from "@/types/services";
import { useLanguage } from "@/i18n/LanguageProvider";
import { cn } from "@/lib/utils";
import { memo } from "react";

interface ProcessStepsSectionProps {
    processSteps: ProcessStep[];
}

export const ProcessStepsSection = memo(function ProcessStepsSection({ processSteps }: ProcessStepsSectionProps) {
    const { t } = useTranslation();
    const { direction } = useLanguage();

    return (
        <section className="py-16 md:py-24">
            <div className="container">
                <div className="text-center max-w-3xl mx-auto mb-16">
                    <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1 mb-4">
                        {t('services.process.title')}
                    </span>
                    <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                        {t('services.process.title')}
                    </h2>
                    <p className="text-lg text-muted-foreground">
                        {t('home.howItWorks.description')}
                    </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
                    {processSteps.map((step) => (
                        <div key={step.id} className="bg-card rounded-xl border border-border p-4 sm:p-6 hover:shadow-md transition-shadow">
                            <div className="flex items-center mb-4">
                                <div className={cn(
                                    "w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center text-primary font-bold",
                                    direction === 'rtl' ? 'ml-3 sm:ml-4' : 'mr-3 sm:mr-4'
                                )}>
                                    {step.number}
                                </div>
                                <step.icon className="h-5 w-5 sm:h-6 sm:w-6 text-secondary" />
                            </div>
                            <h3 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-3 text-foreground">
                                {t(step.titleKey)}
                            </h3>
                            <p className="text-sm sm:text-base text-muted-foreground">
                                {t(step.descriptionKey)}
                            </p>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
});