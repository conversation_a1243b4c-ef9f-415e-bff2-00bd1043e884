import { Mail, MapPin, Phone } from "lucide-react";
import { useTranslation } from "react-i18next";
import BasicHeader from "@/components/layout/BasicHeader";
import Footer from "@/components/layout/Footer";
import { ContactForm } from "@/components/ContactForm";
import { Card, CardContent } from "@/components/ui/card";
import { useLanguage } from "@/i18n/LanguageProvider";

const Contact = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();

  return (
    <div className="min-h-screen flex flex-col">
      <BasicHeader />
      <main className="flex-grow pt-20">
        <section className="py-16 md:py-24">
          <div className="container">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div>
                <span className="inline-block text-sm font-medium text-secondary bg-secondary/10 dark:bg-secondary/20 rounded-full px-3 py-1 mb-4">
                  {t('contact.hero.title')}
                </span>
                <h1 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
                  {t('contact.hero.subtitle')}
                </h1>
                <p className="text-lg text-muted-foreground mb-8">
                  {t('contact.hero.subtitle')}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <Card>
                    <CardContent className={`p-6 flex items-start ${direction === 'rtl' ? 'space-x-reverse' : ''} space-x-4`}>
                      <div className="p-3 bg-primary/10 dark:bg-primary/20 rounded-full">
                        <Mail className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-1">{t('contact.info.email')}</h3>
                        <p className="text-muted-foreground text-sm"><EMAIL></p>
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className={`p-6 flex items-start ${direction === 'rtl' ? 'space-x-reverse' : ''} space-x-4`}>
                      <div className="p-3 bg-primary/10 dark:bg-primary/20 rounded-full">
                        <Phone className="h-5 w-5 text-primary" />
                      </div>
                      <div className="space-y-1">
                        <h3 className="font-medium">{t('contact.info.phone')}</h3>
                        <p className="text-muted-foreground text-sm">+201064149151</p>
                        <a
                          href="https://wa.me/201064149151"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-sm text-primary hover:text-primary/80 transition-colors"
                        >
                          <svg
                            className={`h-4 w-4 ${direction === 'rtl' ? 'ml-1.5' : 'mr-1.5'}`}
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                          >
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z" />
                          </svg>
                          {t('common.whatsappChat')}
                        </a>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="md:col-span-2">
                    <CardContent className={`p-6 flex items-start ${direction === 'rtl' ? 'space-x-reverse' : ''} space-x-4`}>
                      <div className="p-3 bg-primary/10 dark:bg-primary/20 rounded-full">
                        <MapPin className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium mb-1">{t('contact.info.address')}</h3>
                        <p className="text-muted-foreground text-sm">
                          Alexandria<br />
                          Egypt
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="aspect-video w-full rounded-lg overflow-hidden border border-border">
                  {/* Placeholder for map embed */}
                  <div className="w-full h-full bg-muted flex items-center justify-center">
                    <p className="text-muted-foreground">Map Embed Placeholder</p>
                  </div>
                </div>
              </div>

              <div>
                <Card className="border-border">
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold mb-6">{t('contact.form.submit')}</h2>
                    <ContactForm />
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        <section className="py-16 md:py-24 bg-muted/30 dark:bg-muted/10">
          <div className="container">
            <div className="text-center max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-foreground">
                {t('contact.faq.title')}
              </h2>
              <p className="text-lg text-muted-foreground mb-12">
                {t('contact.faq.subtitle')}
              </p>

              <div className="space-y-6 text-left">
                {[
                  {
                    question: "What services does CodeSafir provide?",
                    answer: "CodeSafir offers a comprehensive range of web deployment services including infrastructure as code, CI/CD pipelines, container orchestration, and cloud migrations. We help businesses streamline their deployment processes and improve their web infrastructure."
                  },
                  {
                    question: "How long does a typical project take?",
                    answer: "Project timelines vary depending on the scope and complexity. A simple CI/CD pipeline implementation might take 2-3 weeks, while a complete cloud migration could take 2-3 months. We'll provide a detailed timeline during our initial consultation."
                  },
                  {
                    question: "Do you offer support after project completion?",
                    answer: "Yes, we offer ongoing support and maintenance packages to ensure your deployment infrastructure continues to run smoothly. Our team is available to address any issues and implement updates as needed."
                  },
                  {
                    question: "What cloud providers do you work with?",
                    answer: "We have expertise in all major cloud providers including AWS, Microsoft Azure, Google Cloud Platform, and DigitalOcean. We can help you choose the right provider for your specific needs or work with your existing cloud infrastructure."
                  }
                ].map((faq, index) => (
                  <div key={index} className="bg-card p-6 rounded-lg border border-border">
                    <h3 className="text-xl font-semibold mb-3">{faq.question}</h3>
                    <p className="text-muted-foreground">{faq.answer}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Contact;
