import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeToggle } from '@/components/ThemeToggle';
import { CodeSafirLogo } from '@/components/CodeSafirLogo';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { useLanguage } from '@/i18n/LanguageProvider';
import { cn } from '@/lib/utils';

const BasicHeader: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const { direction } = useLanguage();
  // Set the document direction based on the language
  React.useEffect(() => {
    document.documentElement.dir = direction;
  }, [direction]);
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);

  const navItems = [
    { id: 'home', name: t('nav.home'), path: '/' },
    { id: 'services', name: t('nav.services'), path: '/services' },
    { id: 'about', name: t('nav.about'), path: '/about' },
    { id: 'portfolio', name: t('nav.portfolio'), path: '/portfolio' },
    { id: 'contact', name: t('nav.contact'), path: '/contact' },
  ];

  return (
    <header className="bg-background shadow-sm fixed top-0 left-0 right-0 z-50 w-full">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0 flex items-center">
            <Link to="/">
              <CodeSafirLogo size="sm" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:ml-6 md:flex md:items-center md:space-x-6">
            {navItems.map((item) => {
              const isActive =
                (item.path === '/' && location.pathname === '/') ||
                (item.path !== '/' && location.pathname.startsWith(item.path));

              return (
                <Link
                  key={item.id}
                  to={item.path}
                  className={`px-3 py-2 rounded-md text-sm font-medium border-b-2 ${
                    isActive
                      ? 'text-primary font-semibold border-primary'
                      : 'text-foreground/80 hover:text-primary border-transparent hover:border-primary/50'
                  }`}
                >
                  {item.name}
                </Link>
              );
            })}
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex md:items-center md:space-x-3">
            <LanguageSwitcher variant="ghost" showDropdown />
            <ThemeToggle />
            <Button size="sm">{t('common.getStarted')}</Button>
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <LanguageSwitcher variant="ghost" size="icon" showDropdown />
            <ThemeToggle className="mx-2" />
            <button
              type="button"
              className="inline-flex items-center justify-center p-2 rounded-md text-foreground hover:text-primary hover:bg-muted/50 focus:outline-none"
              aria-controls="mobile-menu"
              aria-expanded="false"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <X className="block h-6 w-6" aria-hidden="true" />
              ) : (
                <Menu className="block h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      <div
        className={`md:hidden ${mobileMenuOpen ? 'block' : 'hidden'} bg-background shadow-lg border-t border-border/20`}
        id="mobile-menu"
      >
        <div className="px-4 pt-3 pb-4 space-y-2 sm:px-5">
          {navItems.map((item) => {
            const isActive =
              (item.path === '/' && location.pathname === '/') ||
              (item.path !== '/' && location.pathname.startsWith(item.path));

            return (
              <Link
                key={item.id}
                to={item.path}
                className={cn(
                  "block px-3 py-2 rounded-md text-base font-medium",
                  isActive
                    ? 'text-primary font-semibold'
                    : 'text-foreground/80 hover:text-primary',
                  direction === 'rtl' ? 'text-right' : 'text-left'
                )}
                onClick={() => setMobileMenuOpen(false)}
              >
                {item.name}
              </Link>
            );
          })}
          <div className="pt-4 mt-4 border-t border-border">
            <Button className="w-full mt-2">{t('common.getStarted')}</Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default BasicHeader;
