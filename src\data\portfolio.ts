import { LucideIcon } from "lucide-react";

export interface Testimonial {
  quote: string;
  author: string;
  role: string;
  company: string;
  image?: string;
}

export interface PortfolioItem {
  id: string;
  title: string;
  client: string;
  description: string;
  challenge: string;
  solution: string;
  results: string;
  technologies: string[];
  category: 'web' | 'ecommerce' | 'cms' | 'mobile' | 'all';
  featured: boolean;
  image: string;
  gallery: string[];
  year: number;
  testimonial?: Testimonial;
}

export const portfolioItems: PortfolioItem[] = [
  {
    id: "tajara-tech",
    title: "Tajara Tech E-commerce Platform",
    client: "Tajara Tech",
    description: "A modern e-commerce platform for a technology retailer with advanced product filtering, user-friendly navigation, and secure payment processing.",
    challenge: "The client needed a robust e-commerce solution that could handle their extensive product catalog while providing an intuitive shopping experience for tech-savvy customers.",
    solution: "We developed a comprehensive e-commerce platform with advanced search and filtering capabilities, responsive design, and seamless checkout process. The solution includes inventory management, customer account features, and integration with multiple payment gateways.",
    results: "The platform has significantly improved user engagement, with a 40% increase in average session duration and a 25% reduction in cart abandonment rate. Mobile conversions have increased by 35% since launch.",
    technologies: ["React", "Node.js", "MongoDB", "Stripe", "Tailwind CSS", "Redux"],
    category: "ecommerce",
    featured: true,
    image: "/portfolio/tajara-tech-1.jpg",
    gallery: [
      "/portfolio/tajara-tech-1.jpg",
      "/portfolio/tajara-tech-2.jpg",
      "/portfolio/tajara-tech-3.jpg"
    ],
    year: 2023,
    testimonial: {
      quote: "CodeSafir delivered an exceptional e-commerce platform that perfectly aligns with our brand and business needs. The user experience is seamless, and we've seen remarkable growth in our online sales since launch.",
      author: "Ahmed Al-Farsi",
      role: "CEO",
      company: "Tajara Tech",
      image: "/testimonials/ahmed-alfarsi.jpg"
    }
  },
  {
    id: "kings-of-ecommerce",
    title: "Kings of E-commerce Platform",
    client: "Kings of E-commerce",
    description: "A premium e-commerce consulting platform showcasing services, case studies, and resources for e-commerce business owners.",
    challenge: "The client needed a professional website that would establish their authority in the e-commerce consulting space while effectively showcasing their services and success stories.",
    solution: "We designed and developed a modern, conversion-focused website with clear service descriptions, detailed case studies, and lead generation forms. The site features a resource hub with downloadable guides and a blog section for industry insights.",
    results: "The new website has increased lead generation by 65% and improved the quality of incoming inquiries. The resource section has become a valuable lead magnet, with over 500 downloads in the first month after launch.",
    technologies: ["Next.js", "TypeScript", "Tailwind CSS", "Contentful", "Vercel", "HubSpot Integration"],
    category: "web",
    featured: true,
    image: "/portfolio/kings-ecommerce-1.jpg",
    gallery: [
      "/portfolio/kings-ecommerce-1.jpg",
      "/portfolio/kings-ecommerce-2.jpg",
      "/portfolio/kings-ecommerce-3.jpg"
    ],
    year: 2023,
    testimonial: {
      quote: "Working with CodeSafir was a game-changer for our business. They understood our vision perfectly and created a website that not only looks professional but also converts visitors into clients effectively.",
      author: "Mohammed Al-Saud",
      role: "Founder",
      company: "Kings of E-commerce",
      image: "/testimonials/mohammed-alsaud.jpg"
    }
  },
  {
    id: "smile-rising",
    title: "Smile Rising Dental Clinic",
    client: "Smile Rising",
    description: "A modern, patient-focused website for a dental clinic with appointment scheduling, service information, and patient testimonials.",
    challenge: "The dental clinic needed a website that would reflect their modern approach to dentistry while making it easy for patients to learn about services and book appointments online.",
    solution: "We designed and developed a clean, professional website with intuitive navigation, detailed service pages, and an integrated appointment booking system. The site includes before/after galleries, patient testimonials, and a blog section for dental health education.",
    results: "The new website has increased online appointment bookings by 75% and improved patient education through the blog content. The clinic has reported that new patients frequently mention the website as their first point of contact.",
    technologies: ["React", "Next.js", "Tailwind CSS", "Calendly Integration", "Strapi CMS", "Vercel"],
    category: "web",
    featured: true,
    image: "/portfolio/smile-rising-1.jpg",
    gallery: [
      "/portfolio/smile-rising-1.jpg",
      "/portfolio/smile-rising-2.jpg",
      "/portfolio/smile-rising-3.jpg"
    ],
    year: 2022,
    testimonial: {
      quote: "Our patients love the new website! It's easy to navigate, beautifully designed, and has significantly increased our online appointment bookings. The team at CodeSafir truly understood our vision and delivered beyond our expectations.",
      author: "Dr. Fatima Al-Zahrani",
      role: "Clinic Director",
      company: "Smile Rising",
      image: "/testimonials/fatima-alzahrani.jpg"
    }
  },
  {
    id: "bu-hamad-co",
    title: "Bu Hamad Co. Corporate Website",
    client: "Bu Hamad Co.",
    description: "A corporate website for a diversified business group showcasing their various business units, projects, and investment opportunities.",
    challenge: "The client needed a professional website that would effectively represent their diverse business portfolio while providing clear information about each business unit and investment opportunity.",
    solution: "We developed a sophisticated corporate website with dedicated sections for each business unit, interactive project showcases, and a secure investor portal. The site features multilingual support (English/Arabic), responsive design, and custom animations to enhance user engagement.",
    results: "The website has strengthened the company's digital presence, resulting in a 40% increase in business inquiries and partnership proposals. The investor portal has streamlined communication with potential investors.",
    technologies: ["React", "TypeScript", "Tailwind CSS", "i18next", "Framer Motion", "Strapi CMS"],
    category: "web",
    featured: true,
    image: "/portfolio/buhamad-1.jpg",
    gallery: [
      "/portfolio/buhamad-1.jpg",
      "/portfolio/buhamad-2.jpg",
      "/portfolio/buhamad-3.jpg"
    ],
    year: 2022,
    testimonial: {
      quote: "CodeSafir delivered a website that perfectly represents our diverse business interests. The multilingual support and investor portal have been particularly valuable for our international operations. We continue to receive compliments on the design and functionality.",
      author: "Khalid Bu Hamad",
      role: "Managing Director",
      company: "Bu Hamad Co.",
      image: "/testimonials/khalid-buhamad.jpg"
    }
  },
  {
    id: "richers-ksa",
    title: "Richers KSA Investment Platform",
    client: "Richers KSA",
    description: "An investment platform showcasing opportunities in Saudi Arabia with detailed project information, financial projections, and investor resources.",
    challenge: "The client needed a platform to attract international investors to Saudi Arabian projects, requiring clear presentation of complex investment information and secure communication channels.",
    solution: "We created a comprehensive investment platform with detailed project listings, interactive financial calculators, and a secure investor dashboard. The site includes market research reports, regulatory information, and a CRM integration for investor relationship management.",
    results: "The platform has facilitated over $5 million in investments within the first year of launch. User engagement metrics show that visitors spend an average of 8 minutes exploring the site, with a 45% return rate for registered users.",
    technologies: ["React", "Node.js", "Express", "MongoDB", "Chart.js", "AWS", "HubSpot CRM"],
    category: "web",
    featured: false,
    image: "/portfolio/richers-ksa-1.jpg",
    gallery: [
      "/portfolio/richers-ksa-1.jpg",
      "/portfolio/richers-ksa-2.jpg",
      "/portfolio/richers-ksa-3.jpg"
    ],
    year: 2022,
    testimonial: {
      quote: "The investment platform developed by CodeSafir has transformed how we present opportunities to international investors. The detailed project listings and financial calculators have made complex investment information accessible and engaging.",
      author: "Saad Al-Qahtani",
      role: "Investment Director",
      company: "Richers KSA",
      image: "/testimonials/saad-alqahtani.jpg"
    }
  },
  {
    id: "zoneart-workspace",
    title: "ZoneArt & Workspace Creative Hub",
    client: "ZoneArt & Workspace",
    description: "A digital platform for a creative coworking space, featuring space booking, event management, and community engagement tools.",
    challenge: "The client needed a website that would not only showcase their creative workspace but also provide functional tools for space booking, event registration, and community building among members.",
    solution: "We developed an integrated platform with a space reservation system, event calendar with registration capabilities, and member profiles with networking features. The site includes virtual tours of the workspace, a blog for community content, and integration with payment gateways.",
    results: "The platform has increased space bookings by 60% and event attendance by 45%. The community features have fostered collaboration between members, resulting in several successful joint projects.",
    technologies: ["React", "Firebase", "Tailwind CSS", "Stripe", "Google Calendar API", "Matterport"],
    category: "web",
    featured: false,
    image: "/portfolio/zoneart-1.jpg",
    gallery: [
      "/portfolio/zoneart-1.jpg",
      "/portfolio/zoneart-2.jpg",
      "/portfolio/zoneart-3.jpg"
    ],
    year: 2021,
    testimonial: {
      quote: "Our coworking space needed more than just a website - we needed a platform that would bring our community together. CodeSafir delivered exactly that, with intuitive booking tools and engaging community features that our members love.",
      author: "Layla Mahmoud",
      role: "Founder",
      company: "ZoneArt & Workspace",
      image: "/testimonials/layla-mahmoud.jpg"
    }
  },
  {
    id: "adams-world",
    title: "Adam's World Children's Furniture Store",
    client: "Adam's World",
    description: "A custom Shopify store for a children's furniture and toy brand, emphasizing user-friendly navigation and an engaging design.",
    challenge: "The client needed an e-commerce store that would appeal to parents while showcasing their premium children's furniture and toys in an engaging way. They required seamless mobile shopping experiences and efficient inventory management.",
    solution: "We designed and developed a custom Shopify store with a playful yet sophisticated design, intuitive product categorization, and high-quality product visualization. We implemented responsive layouts optimized for mobile shopping and integrated multiple payment gateways for a smooth checkout process.",
    results: "The store has achieved a 50% increase in mobile conversions and a 35% improvement in average order value. Customer feedback has been overwhelmingly positive, particularly regarding the ease of navigation and checkout process.",
    technologies: ["Shopify", "Liquid", "JavaScript", "CSS", "Shopify Apps", "Payment Gateways"],
    category: "ecommerce",
    featured: false,
    image: "/portfolio/adams-world-1.jpg",
    gallery: [
      "/portfolio/adams-world-1.jpg",
      "/portfolio/adams-world-2.jpg",
      "/portfolio/adams-world-3.jpg"
    ],
    year: 2021,
    testimonial: {
      quote: "The Shopify store that CodeSafir created for us perfectly captures our brand's playful yet premium aesthetic. The mobile shopping experience is exceptional, and we've seen a significant increase in sales since launch.",
      author: "Adam Al-Nasser",
      role: "Founder",
      company: "Adam's World",
      image: "/testimonials/adam-alnasser.jpg"
    }
  },
  {
    id: "oly-jewels",
    title: "Oly Jewels Luxury Jewelry Store",
    client: "Oly Jewels",
    description: "A customized Shopify store for a luxury jewelry brand with elegant design, product visualization features, and streamlined checkout process.",
    challenge: "The jewelry brand needed an online store that would reflect their luxury aesthetic while providing excellent product visualization and a secure, streamlined purchasing process for high-value items.",
    solution: "We customized a Shopify theme to align with the brand's luxury positioning, implementing high-resolution zoom features for product images and 360-degree product views. We set up automated workflows for inventory and order management, and implemented SEO best practices to boost online visibility.",
    results: "The store has achieved a 70% increase in organic traffic and a 55% improvement in conversion rates. The enhanced product visualization features have reduced return rates by 40% compared to industry averages for online jewelry sales.",
    technologies: ["Shopify", "Liquid", "JavaScript", "CSS", "Shopify Apps", "SEO Optimization"],
    category: "ecommerce",
    featured: false,
    image: "/portfolio/oly-jewels-1.jpg",
    gallery: [
      "/portfolio/oly-jewels-1.jpg",
      "/portfolio/oly-jewels-2.jpg",
      "/portfolio/oly-jewels-3.jpg"
    ],
    year: 2020,
    testimonial: {
      quote: "Our jewelry pieces require exceptional presentation online, and CodeSafir delivered exactly that. The product visualization features have been particularly valuable, significantly reducing return rates and increasing customer confidence in making high-value purchases.",
      author: "Olivia Jewel",
      role: "Creative Director",
      company: "Oly Jewels",
      image: "/testimonials/olivia-jewel.jpg"
    }
  }
];
