import { useEffect, useRef, useState, useCallback } from "react";
import { cn } from "@/lib/utils";

interface CodeAnimationProps {
  className?: string;
}

const TYPING_SPEED = 30;
const PAUSE_BEFORE_DELETE = 3000;

export function CodeAnimation({ className }: CodeAnimationProps) {
  const codeRef = useRef<HTMLPreElement>(null);

  const [lines, setLines] = useState<string[]>([]);
  const [currentLine, setCurrentLine] = useState('');
  const [lineIndex, setLineIndex] = useState(0);
  const [charIndex, setCharIndex] = useState(0);

  const codeLines = [
    'import React, { useState, useEffect } from "react";',
    'import { motion, AnimatePresence } from "framer-motion";',
    '',
    'const WebsiteBuilder = () => {',
    '  const [isLoading, setIsLoading] = useState(true);',
    '  const [components, setComponents] = useState([]);',
    '',
    '  useEffect(() => {',
    '    // Fetch website components from API',
    '    const fetchComponents = async () => {',
    '      try {',
    '        const response = await fetch("/api/components");',
    '        const data = await response.json();',
    '        setComponents(data);',
    '        setIsLoading(false);',
    '      } catch (error) {',
    '        console.error("Error loading components:", error);',
    '      }',
    '    };',
    '',
    '    fetchComponents();',
    '  }, []);',
    '',
    '  // Render responsive components',
    '  return (',
    '    <div className="container mx-auto px-4">',
    '      <header className="py-6">',
    '        <h1 className="text-3xl font-bold text-primary">',
    '          CodeSafir Web Builder',
    '        </h1>',
    '      </header>',
    '',
    '      <main className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">',
    '        <AnimatePresence>',
    '          {!isLoading && components.map((component) => (',
    '            <motion.div',
    '              key={component.id}',
    '              initial={{ opacity: 0, y: 20 }}',
    '              animate={{ opacity: 1, y: 0 }}',
    '              className="bg-white rounded-lg shadow-md p-4"',
    '            >',
    '              <h2 className="text-xl font-semibold mb-2">{component.title}</h2>',
    '              <p className="text-gray-600">{component.description}</p>',
    '            </motion.div>',
    '          ))}',
    '        </AnimatePresence>',
    '      </main>',
    '    </div>',
    '  );',
    '};',
    '',
    'export default WebsiteBuilder;'
  ];

  const typeCode = useCallback(() => {
    const timer = setTimeout(() => {
      if (lineIndex >= codeLines.length) {
        setLineIndex(0);
        setCharIndex(0);
        setLines([]);
        return;
      }

      const currentCodeLine = codeLines[lineIndex];
      if (charIndex < currentCodeLine.length) {
        setCurrentLine(prev => prev + currentCodeLine.charAt(charIndex));
        setCharIndex(prev => prev + 1);
      } else {
        setLines(prev => [...prev, currentLine]);
        setCurrentLine('');
        setLineIndex(prev => prev + 1);
        setCharIndex(0);
      }
    }, charIndex < codeLines[lineIndex]?.length ? TYPING_SPEED : PAUSE_BEFORE_DELETE);

    return () => clearTimeout(timer);
  }, [lineIndex, charIndex, currentLine, codeLines]);

  useEffect(() => {
    if (!codeRef.current) return;

    // Start the typing animation
    const cleanup = typeCode();

    return () => {
      cleanup();
      setLines([]);
      setCurrentLine('');
      setLineIndex(0);
      setCharIndex(0);
    };
  }, [typeCode]);

  return (
    <div className={cn("relative overflow-hidden rounded-lg bg-[#1e1e1e] p-4 shadow-xl", className)}>
      <div className="flex items-center justify-start space-x-2 border-b border-zinc-700 pb-2 mb-3">
        <div className="h-3 w-3 rounded-full bg-red-500"></div>
        <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
        <div className="h-3 w-3 rounded-full bg-green-500"></div>
        <div className="ml-2 text-xs text-zinc-400">WebsiteBuilder.jsx</div>
      </div>
      <pre
        ref={codeRef}
        className="font-mono text-xs sm:text-sm text-blue-400 overflow-x-auto"
      >
        {lines.map((line, index) => (
          <div key={`line-${index}-${line.slice(0, 10)}`}>{line}</div>
        ))}
        {currentLine && (
          <div key={`current-line-${lineIndex}-${charIndex}`}>
            {currentLine}
          </div>
        )}
      </pre>
      <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-[#1e1e1e] to-transparent"></div>
    </div>
  );
}
