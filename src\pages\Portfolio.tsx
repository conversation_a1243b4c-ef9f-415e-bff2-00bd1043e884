import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { Grid3X3, LayoutGrid, Filter } from "lucide-react";
import { motion } from "framer-motion";
import BasicHeader from "@/components/layout/BasicHeader";
import Footer from "@/components/layout/Footer";
import { ProjectCard } from "@/components/ProjectCard";
import { ProjectDetails } from "@/components/ProjectDetails";
import { useLanguage } from "@/i18n/LanguageProvider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CTA from "@/components/sections/CTA";
import { getPortfolioItems, getPortfolioItemBySlug } from "@/lib/sanity";

// Define the PortfolioItem interface based on Sanity schema
interface PortfolioItem {
  _id: string;
  title: string;
  slug: { current: string };
  client: string;
  website?: string;
  description: string;
  technologies: string[];
  publishedAt: string;
  mainImage: any; // Sanity image
  additionalImages?: any[]; // Sanity images array
  detailedDescription?: any; // Sanity portable text
}

const Portfolio = () => {
  const { t } = useTranslation();
  const { direction } = useLanguage();
  const { slug } = useParams();
  const [activeTab, setActiveTab] = useState<string>("all");
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<PortfolioItem[]>([]);
  const [selectedProject, setSelectedProject] = useState<PortfolioItem | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Fetch portfolio items from Sanity
  useEffect(() => {
    const fetchPortfolioItems = async () => {
      try {
        setLoading(true);
        console.log("Fetching portfolio items...");
        const items = await getPortfolioItems();
        console.log("Portfolio items fetched:", items);
        setPortfolioItems(items);
        setFilteredProjects(items);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching portfolio items:", error);
        setLoading(false);
      }
    };

    fetchPortfolioItems();
  }, []);

  // Fetch specific project if slug is provided
  useEffect(() => {
    const fetchProject = async () => {
      if (slug) {
        try {
          setLoading(true);
          console.log("Fetching project with slug:", slug);
          const project = await getPortfolioItemBySlug(slug);
          console.log("Project fetched:", project);
          setSelectedProject(project);
          setLoading(false);
        } catch (error) {
          console.error("Error fetching project:", error);
          setLoading(false);
        }
      } else {
        setSelectedProject(null);
      }
    };

    fetchProject();
  }, [slug]);

  // Filter projects based on active tab
  useEffect(() => {
    if (activeTab === "all") {
      setFilteredProjects(portfolioItems);
    } else {
      // Filter by technologies
      const filtered = portfolioItems.filter(project => 
        project.technologies.some(tech => 
          tech.toLowerCase().includes(activeTab.toLowerCase())
        )
      );
      setFilteredProjects(filtered);
    }
  }, [activeTab, portfolioItems]);

  // Get related projects for the selected project
  const getRelatedProjects = (project: PortfolioItem) => {
    return portfolioItems
      .filter(p => p._id !== project._id)
      .slice(0, 3);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <BasicHeader />
        <main className="flex-grow pt-20 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-muted-foreground">{t('common.loading')}</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // For debugging: display placeholder items if no data from Sanity
  if (portfolioItems.length === 0) {
    console.log("No portfolio items found, using placeholder data");
    // Add fallback placeholder items
    const placeholderItems: PortfolioItem[] = [
      {
        _id: 'placeholder1',
        title: 'Example Project 1',
        slug: { current: 'example-project-1' },
        client: 'Example Client',
        description: 'This is a placeholder project until Sanity data is available.',
        technologies: ['React', 'TypeScript', 'Tailwind CSS'],
        publishedAt: new Date().toISOString(),
        mainImage: null
      },
      {
        _id: 'placeholder2',
        title: 'Example Project 2',
        slug: { current: 'example-project-2' },
        client: 'Example Client 2',
        description: 'Another placeholder project until Sanity data is available.',
        technologies: ['Next.js', 'Node.js', 'MongoDB'],
        publishedAt: new Date().toISOString(),
        mainImage: null
      }
    ];
    
    return (
      <div className="min-h-screen flex flex-col">
        <BasicHeader />
        <main className="flex-grow pt-20">
          <section className="py-16 md:py-24">
            <div className="container">
              <div className="max-w-2xl mx-auto text-center mb-16">
                <h1 className="text-4xl font-bold mb-6 text-foreground">
                  {t('portfolio.title')}
                </h1>
                <p className="text-xl text-muted-foreground">
                  {t('portfolio.description')}
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {placeholderItems.map((project, index) => (
                  <ProjectCard key={project._id} project={project} index={index} />
                ))}
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-b from-background to-muted/30 dark:from-background dark:to-background/80">
      <BasicHeader />
      <main className="flex-grow pt-20">
        {selectedProject ? (
          // Single project view using ProjectDetails component
          <>
            <ProjectDetails 
              project={selectedProject} 
              relatedProjects={getRelatedProjects(selectedProject)} 
            />
            <CTA />
          </>
        ) : (
          // Portfolio grid view
          <>
            <section className="py-16 md:py-24">
              <div className="container max-w-7xl mx-auto">
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="max-w-2xl mx-auto text-center mb-16"
                >
                  <h1 className="text-4xl md:text-5xl font-bold mb-6 text-foreground">
                    {t('portfolio.title')}
                  </h1>
                  <p className="text-xl text-muted-foreground">
                    {t('portfolio.description')}
                  </p>
                </motion.div>

                {/* Filter Tabs */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="flex justify-center mb-12"
                >
                  <Tabs defaultValue="all" className="w-full max-w-2xl" onValueChange={setActiveTab}>
                    <TabsList className="grid grid-cols-2 md:grid-cols-5 mb-8">
                      <TabsTrigger value="all">{t('portfolio.filters.all')}</TabsTrigger>
                      <TabsTrigger value="web">{t('portfolio.filters.web')}</TabsTrigger>
                      <TabsTrigger value="ecommerce">{t('portfolio.filters.ecommerce')}</TabsTrigger>
                      <TabsTrigger value="cms">{t('portfolio.filters.cms')}</TabsTrigger>
                      <TabsTrigger value="mobile">{t('portfolio.filters.mobile')}</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </motion.div>

                {/* Featured Project (if available) */}
                {filteredProjects.length > 0 && (
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    className="mb-12"
                  >
                    <ProjectCard 
                      project={filteredProjects[0]} 
                      featured={true} 
                      index={0} 
                    />
                  </motion.div>
                )}

                {/* Portfolio grid */}
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                >
                  {filteredProjects.slice(1).map((project, index) => (
                    <ProjectCard key={project._id} project={project} index={index + 1} />
                  ))}
                </motion.div>

                {filteredProjects.length === 0 && (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">No projects found matching your filter criteria.</p>
                  </div>
                )}
              </div>
            </section>

            {/* CTA Section */}
            <CTA />
          </>
        )}
      </main>
      <Footer />
    </div>
  );
};

export default Portfolio;
